<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Metal Rate Details') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('metal-rates.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Metal Rates
                </a>
                @can('edit_metal_rate')
                    <a href="{{ route('metal-rates.edit', $metalRate) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Edit Rate
                    </a>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Rate Details Card -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Rate Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Metal Type</label>
                            <div class="mt-1 text-lg font-semibold text-gray-900">{{ $metalRate->metal_type }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Purity</label>
                            <div class="mt-1 text-lg font-semibold text-gray-900">{{ $metalRate->purity }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Rate per Gram</label>
                            <div class="mt-1 text-lg font-semibold text-green-600">₹{{ number_format($metalRate->rate_per_gram, 2) }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Rate per 10 Grams</label>
                            <div class="mt-1 text-lg font-semibold text-green-600">₹{{ number_format($metalRate->rate_per_10_gram, 2) }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Effective Date</label>
                            <div class="mt-1 text-lg font-semibold text-gray-900">{{ $metalRate->effective_date->format('d M, Y') }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <div class="mt-1">
                                @if($metalRate->is_active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Inactive
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Created By</label>
                            <div class="mt-1 text-lg font-semibold text-gray-900">{{ $metalRate->createdBy->name ?? 'Unknown' }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Created On</label>
                            <div class="mt-1 text-lg font-semibold text-gray-900">{{ $metalRate->created_at->format('d M, Y H:i') }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rate History -->
            @if($rateHistory->count() > 0)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Rate History for {{ $metalRate->metal_type }} {{ $metalRate->purity }}</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate per Gram</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate per 10g</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Effective Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($rateHistory as $rate)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">₹{{ number_format($rate->rate_per_gram, 2) }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">₹{{ number_format($rate->rate_per_10_gram, 2) }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">{{ $rate->effective_date->format('d M, Y') }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                @if($rate->is_active)
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        Active
                                                    </span>
                                                @else
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                        Inactive
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">{{ $rate->createdBy->name ?? 'Unknown' }}</div>
                                                <div class="text-sm text-gray-500">{{ $rate->created_at->format('d M, Y') }}</div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
