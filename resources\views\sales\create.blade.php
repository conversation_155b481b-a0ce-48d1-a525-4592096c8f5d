<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create New Sale') }}
            </h2>
            <a href="{{ route('sales.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Sales
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('sales.store') }}" id="saleForm">
                        @csrf

                        <!-- Customer and Date Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Sale Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="customer_id" class="block text-sm font-medium text-gray-700">Customer *</label>
                                    <select name="customer_id" id="customer_id" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Customer</option>
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer->id }}" {{ old('customer_id') == $customer->id ? 'selected' : '' }}>
                                                {{ $customer->name }} - {{ $customer->mobile }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('customer_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="sale_date" class="block text-sm font-medium text-gray-700">Sale Date *</label>
                                    <input type="date" name="sale_date" id="sale_date" value="{{ old('sale_date', date('Y-m-d')) }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('sale_date')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Products Section -->
                        <div class="mb-8">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">Products</h3>
                                <button type="button" id="addProduct" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                    Add Product
                                </button>
                            </div>

                            <div id="productsContainer">
                                <!-- Product items will be added here dynamically -->
                            </div>
                        </div>

                        <!-- Billing Summary -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Billing Summary</h3>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div class="flex justify-between">
                                        <span>Subtotal:</span>
                                        <span id="subtotalDisplay">₹0.00</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>CGST (1.5%):</span>
                                        <span id="cgstDisplay">₹0.00</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>SGST (1.5%):</span>
                                        <span id="sgstDisplay">₹0.00</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Total Tax:</span>
                                        <span id="totalTaxDisplay">₹0.00</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Discount:</span>
                                        <span>
                                            <input type="number" name="discount_amount" id="discount_amount" value="0" step="0.01" min="0"
                                                   class="w-20 text-right border-gray-300 rounded text-sm">
                                        </span>
                                    </div>
                                    <div class="flex justify-between font-bold text-lg border-t pt-2">
                                        <span>Total Amount:</span>
                                        <span id="totalAmountDisplay">₹0.00</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="cash_payment" class="block text-sm font-medium text-gray-700">Cash Payment</label>
                                    <input type="number" name="cash_payment" id="cash_payment" value="0" step="0.01" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                </div>

                                <div>
                                    <label for="card_payment" class="block text-sm font-medium text-gray-700">Card Payment</label>
                                    <input type="number" name="card_payment" id="card_payment" value="0" step="0.01" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                </div>

                                <div>
                                    <label for="upi_payment" class="block text-sm font-medium text-gray-700">UPI Payment</label>
                                    <input type="number" name="upi_payment" id="upi_payment" value="0" step="0.01" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                </div>

                                <div>
                                    <label for="old_gold_adjustment" class="block text-sm font-medium text-gray-700">Old Gold Adjustment</label>
                                    <div class="flex space-x-2">
                                        <input type="number" name="old_gold_adjustment" id="old_gold_adjustment" value="0" step="0.01" min="0"
                                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <button type="button" id="selectOldGold" class="mt-1 bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded whitespace-nowrap">
                                            Select Old Gold
                                        </button>
                                    </div>
                                    <div id="selectedOldGold" class="mt-2 text-sm text-gray-600"></div>
                                </div>
                            </div>

                            <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                                <div class="flex justify-between items-center">
                                    <span class="font-medium">Total Payment:</span>
                                    <span id="totalPaymentDisplay" class="font-bold text-lg">₹0.00</span>
                                </div>
                                <div class="flex justify-between items-center mt-2">
                                    <span class="font-medium">Balance Amount:</span>
                                    <span id="balanceAmountDisplay" class="font-bold text-lg text-red-600">₹0.00</span>
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-8">
                            <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                            <textarea name="notes" id="notes" rows="3"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">{{ old('notes') }}</textarea>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-4">
                            <a href="{{ route('sales.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Create Sale
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Row Template -->
    <template id="productRowTemplate">
        <div class="product-row border border-gray-200 rounded-lg p-4 mb-4">
            <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700">Product</label>
                    <select name="items[INDEX][product_id]" class="product-select mt-1 block w-full rounded-md border-gray-300 shadow-sm" required>
                        <option value="">Select Product</option>
                        @foreach($products as $product)
                            <option value="{{ $product->id }}" 
                                    data-name="{{ $product->name }}"
                                    data-metal-type="{{ $product->metal_type }}"
                                    data-purity="{{ $product->purity }}"
                                    data-weight="{{ $product->net_weight }}"
                                    data-making-charges="{{ $product->making_charges }}"
                                    data-stone-charges="{{ $product->stone_charges }}"
                                    data-price="{{ $product->selling_price }}"
                                    data-max-qty="{{ $product->quantity }}">
                                {{ $product->name }} - {{ $product->barcode }} (₹{{ number_format($product->selling_price, 2) }})
                            </option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Quantity</label>
                    <input type="number" name="items[INDEX][quantity]" class="quantity-input mt-1 block w-full rounded-md border-gray-300 shadow-sm" min="1" value="1" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Metal Rate</label>
                    <input type="number" name="items[INDEX][metal_rate]" class="metal-rate-input mt-1 block w-full rounded-md border-gray-300 shadow-sm" step="0.01" min="0" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Making Charges</label>
                    <input type="number" name="items[INDEX][making_charges]" class="making-charges-input mt-1 block w-full rounded-md border-gray-300 shadow-sm" step="0.01" min="0" required>
                </div>
                <div class="flex items-end">
                    <button type="button" class="remove-product bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                        Remove
                    </button>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Stone Charges</label>
                    <input type="number" name="items[INDEX][stone_charges]" class="stone-charges-input mt-1 block w-full rounded-md border-gray-300 shadow-sm" step="0.01" min="0" value="0">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Wastage Amount</label>
                    <input type="number" name="items[INDEX][wastage_amount]" class="wastage-amount-input mt-1 block w-full rounded-md border-gray-300 shadow-sm" step="0.01" min="0" value="0">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Item Total</label>
                    <input type="number" name="items[INDEX][item_total]" class="item-total-input mt-1 block w-full rounded-md border-gray-300 shadow-sm bg-gray-100" step="0.01" min="0" readonly required>
                </div>
                <div class="flex items-end">
                    <div class="text-sm text-gray-600">
                        <div class="product-details"></div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- Old Gold Selection Modal -->
    <div id="oldGoldModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-6 border w-11/12 md:w-3/4 lg:w-1/2 max-w-2xl shadow-xl rounded-lg bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold text-gray-900">Select Old Gold Vouchers</h3>
                    <button type="button" id="closeOldGoldModal" class="text-gray-400 hover:text-gray-600 text-2xl font-bold">
                        &times;
                    </button>
                </div>

                <div id="oldGoldLoadingIndicator" class="hidden text-center py-4">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading vouchers...</p>
                </div>

                <div id="oldGoldList" class="space-y-3 max-h-80 overflow-y-auto border rounded-lg p-4 bg-gray-50">
                    <!-- Old gold vouchers will be loaded here -->
                </div>

                <div class="flex justify-between items-center mt-6">
                    <div id="selectedVouchersSummary" class="text-sm text-gray-600"></div>
                    <div class="flex space-x-3">
                        <button type="button" id="cancelOldGold" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors">
                            Cancel
                        </button>
                        <button type="button" id="confirmOldGold" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                            Confirm Selection
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let productIndex = 0;
        const products = @json($products);
        const metalRates = @json($metalRates);
        let selectedOldGoldVouchers = [];

        document.addEventListener('DOMContentLoaded', function() {
            const addProductBtn = document.getElementById('addProduct');
            const productsContainer = document.getElementById('productsContainer');
            const discountInput = document.getElementById('discount_amount');
            const paymentInputs = ['cash_payment', 'card_payment', 'upi_payment', 'old_gold_adjustment'];

            // Add first product row
            addProductRow();

            // Add product button
            addProductBtn.addEventListener('click', addProductRow);

            // Update calculations when discount changes
            discountInput.addEventListener('input', updateTotals);

            // Update calculations when payment amounts change
            paymentInputs.forEach(inputId => {
                document.getElementById(inputId).addEventListener('input', updatePaymentTotals);
            });

            // Old gold selection
            document.getElementById('selectOldGold').addEventListener('click', function() {
                const customerId = document.getElementById('customer_id').value;
                if (!customerId) {
                    alert('Please select a customer first');
                    return;
                }
                loadOldGoldVouchers(customerId);
            });

            document.getElementById('cancelOldGold').addEventListener('click', function() {
                closeOldGoldModal();
            });

            document.getElementById('closeOldGoldModal').addEventListener('click', function() {
                closeOldGoldModal();
            });

            document.getElementById('confirmOldGold').addEventListener('click', function() {
                const totalVoucherAmount = selectedOldGoldVouchers.reduce((sum, voucher) => sum + voucher.amount, 0);
                document.getElementById('old_gold_adjustment').value = totalVoucherAmount.toFixed(2);

                // Update display
                const selectedDisplay = document.getElementById('selectedOldGold');
                if (selectedOldGoldVouchers.length > 0) {
                    selectedDisplay.innerHTML = `Selected ${selectedOldGoldVouchers.length} voucher(s) worth ₹${totalVoucherAmount.toFixed(2)}`;
                } else {
                    selectedDisplay.innerHTML = '';
                }

                closeOldGoldModal();
                updatePaymentTotals();
            });

            // Close modal when clicking outside
            document.getElementById('oldGoldModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeOldGoldModal();
                }
            });

            function addProductRow() {
                const template = document.getElementById('productRowTemplate');
                const clone = template.content.cloneNode(true);
                
                // Replace INDEX with actual index
                const html = clone.querySelector('.product-row').outerHTML.replace(/INDEX/g, productIndex);
                productsContainer.insertAdjacentHTML('beforeend', html);
                
                // Add event listeners to the new row
                const newRow = productsContainer.lastElementChild;
                setupProductRow(newRow);
                
                productIndex++;
            }

            function setupProductRow(row) {
                const productSelect = row.querySelector('.product-select');
                const quantityInput = row.querySelector('.quantity-input');
                const metalRateInput = row.querySelector('.metal-rate-input');
                const makingChargesInput = row.querySelector('.making-charges-input');
                const stoneChargesInput = row.querySelector('.stone-charges-input');
                const wastageAmountInput = row.querySelector('.wastage-amount-input');
                const itemTotalInput = row.querySelector('.item-total-input');
                const removeBtn = row.querySelector('.remove-product');
                const productDetails = row.querySelector('.product-details');

                // Product selection
                productSelect.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    if (selectedOption.value) {
                        const productData = selectedOption.dataset;
                        
                        // Set default values
                        metalRateInput.value = getMetalRate(productData.metalType, productData.purity);
                        makingChargesInput.value = productData.makingCharges;
                        stoneChargesInput.value = productData.stoneCharges;
                        
                        // Update product details
                        productDetails.innerHTML = `
                            ${productData.metalType} ${productData.purity} - ${productData.weight}g<br>
                            Max Qty: ${productData.maxQty}
                        `;
                        
                        // Set max quantity
                        quantityInput.max = productData.maxQty;
                        
                        calculateItemTotal();
                    }
                });

                // Calculate item total when inputs change
                [quantityInput, metalRateInput, makingChargesInput, stoneChargesInput, wastageAmountInput].forEach(input => {
                    input.addEventListener('input', calculateItemTotal);
                });

                // Remove product row
                removeBtn.addEventListener('click', function() {
                    row.remove();
                    updateTotals();
                });

                function calculateItemTotal() {
                    const quantity = parseFloat(quantityInput.value) || 0;
                    const metalRate = parseFloat(metalRateInput.value) || 0;
                    const makingCharges = parseFloat(makingChargesInput.value) || 0;
                    const stoneCharges = parseFloat(stoneChargesInput.value) || 0;
                    const wastageAmount = parseFloat(wastageAmountInput.value) || 0;
                    
                    const total = (metalRate + makingCharges + stoneCharges + wastageAmount) * quantity;
                    itemTotalInput.value = total.toFixed(2);
                    
                    updateTotals();
                }
            }

            function getMetalRate(metalType, purity) {
                const rateKey = `${metalType}.${purity}`;
                return metalRates[rateKey]?.rate_per_gram || 0;
            }

            function updateTotals() {
                let subtotal = 0;
                
                document.querySelectorAll('.item-total-input').forEach(input => {
                    subtotal += parseFloat(input.value) || 0;
                });
                
                const discount = parseFloat(discountInput.value) || 0;
                const cgst = subtotal * 0.015; // 1.5%
                const sgst = subtotal * 0.015; // 1.5%
                const totalTax = cgst + sgst;
                const totalAmount = subtotal + totalTax - discount;
                
                document.getElementById('subtotalDisplay').textContent = `₹${subtotal.toFixed(2)}`;
                document.getElementById('cgstDisplay').textContent = `₹${cgst.toFixed(2)}`;
                document.getElementById('sgstDisplay').textContent = `₹${sgst.toFixed(2)}`;
                document.getElementById('totalTaxDisplay').textContent = `₹${totalTax.toFixed(2)}`;
                document.getElementById('totalAmountDisplay').textContent = `₹${totalAmount.toFixed(2)}`;
                
                updatePaymentTotals();
            }

            function updatePaymentTotals() {
                const totalAmount = parseFloat(document.getElementById('totalAmountDisplay').textContent.replace('₹', '').replace(',', '')) || 0;

                let totalPayment = 0;
                paymentInputs.forEach(inputId => {
                    totalPayment += parseFloat(document.getElementById(inputId).value) || 0;
                });

                const balance = totalAmount - totalPayment;

                document.getElementById('totalPaymentDisplay').textContent = `₹${totalPayment.toFixed(2)}`;
                document.getElementById('balanceAmountDisplay').textContent = `₹${balance.toFixed(2)}`;
                document.getElementById('balanceAmountDisplay').className = balance > 0 ? 'font-bold text-lg text-red-600' : 'font-bold text-lg text-green-600';
            }

            function loadOldGoldVouchers(customerId) {
                // Show loading indicator
                const loadingIndicator = document.getElementById('oldGoldLoadingIndicator');
                const oldGoldList = document.getElementById('oldGoldList');

                loadingIndicator.classList.remove('hidden');
                oldGoldList.innerHTML = '';
                document.getElementById('oldGoldModal').classList.remove('hidden');

                // Clear previous selections
                selectedOldGoldVouchers = [];
                updateVouchersSummary();

                fetch(`/old-gold-purchases/customer/${customerId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(vouchers => {
                        loadingIndicator.classList.add('hidden');
                        oldGoldList.innerHTML = '';

                        if (vouchers.length === 0) {
                            oldGoldList.innerHTML = `
                                <div class="text-center py-8">
                                    <div class="text-gray-400 text-6xl mb-4">📋</div>
                                    <p class="text-gray-500 text-lg font-medium">No available old gold vouchers</p>
                                    <p class="text-gray-400 text-sm mt-2">This customer doesn't have any unused old gold vouchers.</p>
                                </div>
                            `;
                        } else {
                            vouchers.forEach(voucher => {
                                const voucherDiv = document.createElement('div');
                                voucherDiv.className = 'border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:bg-blue-50 transition-colors bg-white';
                                voucherDiv.innerHTML = `
                                    <label class="flex items-center cursor-pointer">
                                        <input type="checkbox" class="old-gold-checkbox mr-4 h-4 w-4 text-blue-600 rounded focus:ring-blue-500"
                                               data-voucher-id="${voucher.id}"
                                               data-amount="${voucher.voucher_amount}"
                                               data-purchase-number="${voucher.purchase_number}">
                                        <div class="flex-1">
                                            <div class="font-semibold text-gray-900">${voucher.purchase_number}</div>
                                            <div class="text-sm text-gray-600 mt-1">${voucher.metal_type} ${voucher.purity} - ${voucher.final_weight}g</div>
                                            <div class="text-lg font-bold text-green-600 mt-1">₹${parseFloat(voucher.voucher_amount).toFixed(2)}</div>
                                        </div>
                                    </label>
                                `;
                                oldGoldList.appendChild(voucherDiv);
                            });

                            // Add event listeners to checkboxes
                            document.querySelectorAll('.old-gold-checkbox').forEach(checkbox => {
                                checkbox.addEventListener('change', function() {
                                    const voucherId = this.dataset.voucherId;
                                    const amount = parseFloat(this.dataset.amount);
                                    const purchaseNumber = this.dataset.purchaseNumber;

                                    if (this.checked) {
                                        selectedOldGoldVouchers.push({
                                            id: voucherId,
                                            amount: amount,
                                            purchase_number: purchaseNumber
                                        });
                                    } else {
                                        selectedOldGoldVouchers = selectedOldGoldVouchers.filter(v => v.id !== voucherId);
                                    }
                                    updateVouchersSummary();
                                });
                            });
                        }
                    })
                    .catch(error => {
                        loadingIndicator.classList.add('hidden');
                        console.error('Error loading old gold vouchers:', error);
                        oldGoldList.innerHTML = `
                            <div class="text-center py-8">
                                <div class="text-red-400 text-6xl mb-4">⚠️</div>
                                <p class="text-red-500 text-lg font-medium">Error loading vouchers</p>
                                <p class="text-gray-400 text-sm mt-2">Please try again or contact support if the problem persists.</p>
                            </div>
                        `;
                    });
            }

            function closeOldGoldModal() {
                document.getElementById('oldGoldModal').classList.add('hidden');
                // Clear any loading states
                document.getElementById('oldGoldLoadingIndicator').classList.add('hidden');
            }

            function updateVouchersSummary() {
                const summaryElement = document.getElementById('selectedVouchersSummary');
                if (selectedOldGoldVouchers.length === 0) {
                    summaryElement.textContent = '';
                } else {
                    const totalAmount = selectedOldGoldVouchers.reduce((sum, voucher) => sum + voucher.amount, 0);
                    summaryElement.textContent = `${selectedOldGoldVouchers.length} voucher(s) selected - Total: ₹${totalAmount.toFixed(2)}`;
                }
            }
        });
    </script>
</x-app-layout>
