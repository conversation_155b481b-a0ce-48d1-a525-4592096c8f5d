<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MetalRate>
 */
class MetalRateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $metalTypes = ['Gold', 'Silver', 'Platinum'];
        $goldPurities = ['24K', '22K', '18K', '14K', '10K'];
        $silverPurities = ['999', '925'];
        $platinumPurities = ['950', '900'];
        
        $metalType = fake()->randomElement($metalTypes);
        
        switch ($metalType) {
            case 'Gold':
                $purity = fake()->randomElement($goldPurities);
                $ratePerGram = fake()->randomFloat(2, 4500, 6500);
                break;
            case 'Silver':
                $purity = fake()->randomElement($silverPurities);
                $ratePerGram = fake()->randomFloat(2, 60, 100);
                break;
            case 'Platinum':
                $purity = fake()->randomElement($platinumPurities);
                $ratePerGram = fake()->randomFloat(2, 2500, 3500);
                break;
            default:
                $purity = '22K';
                $ratePerGram = 5000.00;
        }

        return [
            'metal_type' => $metalType,
            'purity' => $purity,
            'rate_per_gram' => $ratePerGram,
            'rate_per_10_gram' => $ratePerGram * 10,
            'effective_date' => fake()->dateTimeBetween('-1 month', 'now')->format('Y-m-d'),
            'is_active' => fake()->boolean(80), // 80% chance of being active
            'created_by' => User::factory(),
        ];
    }

    /**
     * Indicate that the metal rate is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'effective_date' => today()->format('Y-m-d'),
        ]);
    }

    /**
     * Indicate that the metal rate is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a gold rate.
     */
    public function gold(): static
    {
        return $this->state(fn (array $attributes) => [
            'metal_type' => 'Gold',
            'purity' => '22K',
            'rate_per_gram' => fake()->randomFloat(2, 4500, 6500),
        ]);
    }

    /**
     * Create a silver rate.
     */
    public function silver(): static
    {
        return $this->state(fn (array $attributes) => [
            'metal_type' => 'Silver',
            'purity' => '925',
            'rate_per_gram' => fake()->randomFloat(2, 60, 100),
        ]);
    }
}
