<?php

namespace App\Helpers;

use App\Models\MetalRate;
use Illuminate\Support\Facades\Cache;

class JewelHelper
{
    /**
     * Format currency amount
     */
    public static function formatCurrency($amount, $symbol = '₹')
    {
        return $symbol . number_format($amount, 2);
    }

    /**
     * Format weight with unit
     */
    public static function formatWeight($weight, $unit = 'g')
    {
        return number_format($weight, 3) . $unit;
    }

    /**
     * Calculate GST amounts
     */
    public static function calculateGST($amount, $rate = null)
    {
        $rate = $rate ?? config('jewel.defaults.gst_rate', 3.0);
        $gstAmount = ($amount * $rate) / 100;
        
        return [
            'cgst' => $gstAmount / 2,
            'sgst' => $gstAmount / 2,
            'igst' => $gstAmount,
            'total' => $gstAmount,
        ];
    }

    /**
     * Calculate wastage amount
     */
    public static function calculateWastage($netWeight, $percentage = null)
    {
        $percentage = $percentage ?? config('jewel.defaults.wastage_percentage', 8.0);
        return ($netWeight * $percentage) / 100;
    }

    /**
     * Calculate melting loss
     */
    public static function calculateMeltingLoss($netWeight, $percentage = null)
    {
        $percentage = $percentage ?? config('jewel.defaults.melting_loss_percentage', 2.0);
        return ($netWeight * $percentage) / 100;
    }

    /**
     * Get current metal rate
     */
    public static function getCurrentMetalRate($metalType, $purity)
    {
        $cacheKey = "metal_rate_{$metalType}_{$purity}";
        
        return Cache::remember($cacheKey, config('jewel.cache.metal_rates_ttl', 300), function () use ($metalType, $purity) {
            return MetalRate::where('metal_type', $metalType)
                ->where('purity', $purity)
                ->where('is_active', true)
                ->where('effective_date', '<=', today())
                ->orderBy('effective_date', 'desc')
                ->first();
        });
    }

    /**
     * Generate unique barcode
     */
    public static function generateBarcode($prefix = '2')
    {
        do {
            $barcode = $prefix . str_pad(mt_rand(0, 999999999999), 12, '0', STR_PAD_LEFT);
        } while (\App\Models\Product::where('barcode', $barcode)->exists());

        return $barcode;
    }

    /**
     * Generate invoice number
     */
    public static function generateInvoiceNumber($prefix = 'INV')
    {
        $year = date('Y');
        $count = \App\Models\Sale::whereYear('created_at', $year)->count() + 1;
        return $prefix . '-' . $year . '-' . str_pad($count, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Generate purchase number
     */
    public static function generatePurchaseNumber($prefix = 'OGP')
    {
        $year = date('Y');
        $count = \App\Models\OldGoldPurchase::whereYear('created_at', $year)->count() + 1;
        return $prefix . '-' . $year . '-' . str_pad($count, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Generate estimate number
     */
    public static function generateEstimateNumber($prefix = 'EST')
    {
        $year = date('Y');
        $count = \App\Models\Estimate::whereYear('created_at', $year)->count() + 1;
        return $prefix . '-' . $year . '-' . str_pad($count, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Validate Indian mobile number
     */
    public static function isValidMobile($mobile)
    {
        return preg_match('/^[6-9]\d{9}$/', $mobile);
    }

    /**
     * Validate GSTIN
     */
    public static function isValidGSTIN($gstin)
    {
        return preg_match('/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/', $gstin);
    }

    /**
     * Validate PAN
     */
    public static function isValidPAN($pan)
    {
        return preg_match('/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/', $pan);
    }

    /**
     * Get business configuration
     */
    public static function getBusinessConfig($key = null)
    {
        $config = config('jewel.business');
        return $key ? ($config[$key] ?? null) : $config;
    }

    /**
     * Check if feature is enabled
     */
    public static function isFeatureEnabled($feature)
    {
        return config("jewel.features.{$feature}", false);
    }

    /**
     * Get pagination limit
     */
    public static function getPaginationLimit($default = null)
    {
        return $default ?? config('jewel.pagination.per_page', 15);
    }

    /**
     * Clear all jewelry-related caches
     */
    public static function clearAllCaches()
    {
        Cache::forget('current_metal_rates');
        Cache::forget('dashboard_stats');
        
        // Clear individual metal rate caches
        $metalTypes = ['Gold', 'Silver', 'Platinum'];
        $purities = ['24K', '22K', '18K', '14K', '10K', '999', '925'];
        
        foreach ($metalTypes as $metal) {
            foreach ($purities as $purity) {
                Cache::forget("metal_rate_{$metal}_{$purity}");
            }
        }
    }
}
