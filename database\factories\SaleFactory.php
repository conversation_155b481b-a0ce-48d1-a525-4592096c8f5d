<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Customer;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Sale>
 */
class SaleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $subtotal = fake()->randomFloat(2, 1000, 50000);
        $totalTax = $subtotal * 0.03; // 3% GST
        $discountAmount = fake()->randomFloat(2, 0, $subtotal * 0.1);
        $totalAmount = $subtotal + $totalTax - $discountAmount;
        
        $cashPayment = fake()->randomFloat(2, 0, $totalAmount);
        $cardPayment = fake()->randomFloat(2, 0, $totalAmount - $cashPayment);
        $upiPayment = fake()->randomFloat(2, 0, $totalAmount - $cashPayment - $cardPayment);
        $oldGoldAdjustment = fake()->randomFloat(2, 0, $totalAmount - $cashPayment - $cardPayment - $upiPayment);
        
        $totalPayment = $cashPayment + $cardPayment + $upiPayment + $oldGoldAdjustment;
        $balanceAmount = $totalAmount - $totalPayment;
        
        $paymentStatus = 'paid';
        if ($balanceAmount > 0) {
            $paymentStatus = $totalPayment > 0 ? 'partial' : 'pending';
        }

        return [
            'invoice_number' => 'INV-' . date('Y') . '-' . fake()->unique()->numerify('######'),
            'customer_id' => Customer::factory(),
            'sale_date' => fake()->dateTimeBetween('-1 year', 'now')->format('Y-m-d'),
            'subtotal' => $subtotal,
            'cgst_amount' => $totalTax / 2,
            'sgst_amount' => $totalTax / 2,
            'igst_amount' => 0,
            'total_tax' => $totalTax,
            'discount_amount' => $discountAmount,
            'total_amount' => $totalAmount,
            'cash_payment' => $cashPayment,
            'card_payment' => $cardPayment,
            'upi_payment' => $upiPayment,
            'old_gold_adjustment' => $oldGoldAdjustment,
            'balance_amount' => $balanceAmount,
            'payment_status' => $paymentStatus,
            'notes' => fake()->optional()->sentence(),
            'created_by' => User::factory(),
        ];
    }

    /**
     * Indicate that the sale is pending payment.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'cash_payment' => 0,
            'card_payment' => 0,
            'upi_payment' => 0,
            'old_gold_adjustment' => 0,
            'balance_amount' => $attributes['total_amount'],
            'payment_status' => 'pending',
        ]);
    }

    /**
     * Indicate that the sale has partial payment.
     */
    public function partial(): static
    {
        return $this->state(function (array $attributes) {
            $partialPayment = $attributes['total_amount'] * 0.5;
            return [
                'cash_payment' => $partialPayment,
                'card_payment' => 0,
                'upi_payment' => 0,
                'old_gold_adjustment' => 0,
                'balance_amount' => $attributes['total_amount'] - $partialPayment,
                'payment_status' => 'partial',
            ];
        });
    }
}
