<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit Metal Rate') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('metal-rates.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Metal Rates
                </a>
                @can('view_metal_rates')
                    <a href="{{ route('metal-rates.show', $metalRate) }}" class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
                        View Details
                    </a>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('metal-rates.update', $metalRate) }}">
                        @csrf
                        @method('PUT')

                        <!-- Metal Type -->
                        <div class="mb-4">
                            <label for="metal_type" class="block text-sm font-medium text-gray-700">Metal Type</label>
                            <select id="metal_type" name="metal_type" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">Select Metal Type</option>
                                <option value="Gold" {{ old('metal_type', $metalRate->metal_type) == 'Gold' ? 'selected' : '' }}>Gold</option>
                                <option value="Silver" {{ old('metal_type', $metalRate->metal_type) == 'Silver' ? 'selected' : '' }}>Silver</option>
                                <option value="Platinum" {{ old('metal_type', $metalRate->metal_type) == 'Platinum' ? 'selected' : '' }}>Platinum</option>
                            </select>
                            @error('metal_type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Purity -->
                        <div class="mb-4">
                            <label for="purity" class="block text-sm font-medium text-gray-700">Purity</label>
                            <select id="purity" name="purity" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">Select Purity</option>
                                <!-- Options will be populated by JavaScript based on metal type -->
                            </select>
                            @error('purity')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Rate per Gram -->
                        <div class="mb-4">
                            <label for="rate_per_gram" class="block text-sm font-medium text-gray-700">Rate per Gram (₹)</label>
                            <input type="number" id="rate_per_gram" name="rate_per_gram" step="0.01" min="0" 
                                   value="{{ old('rate_per_gram', $metalRate->rate_per_gram) }}" required
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            @error('rate_per_gram')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Rate per 10 Grams (Auto-calculated) -->
                        <div class="mb-4">
                            <label for="rate_per_10_gram" class="block text-sm font-medium text-gray-700">Rate per 10 Grams (₹)</label>
                            <input type="number" id="rate_per_10_gram" step="0.01" readonly
                                   value="{{ old('rate_per_10_gram', $metalRate->rate_per_10_gram) }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <p class="mt-1 text-sm text-gray-500">This field is automatically calculated.</p>
                        </div>

                        <!-- Effective Date -->
                        <div class="mb-4">
                            <label for="effective_date" class="block text-sm font-medium text-gray-700">Effective Date</label>
                            <input type="date" id="effective_date" name="effective_date" 
                                   value="{{ old('effective_date', $metalRate->effective_date->format('Y-m-d')) }}" required
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            @error('effective_date')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Is Active -->
                        <div class="mb-6">
                            <div class="flex items-center">
                                <input type="checkbox" id="is_active" name="is_active" value="1" 
                                       {{ old('is_active', $metalRate->is_active) ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                    Set as Active Rate
                                </label>
                            </div>
                            <p class="mt-1 text-sm text-gray-500">If checked, this will become the current active rate for this metal and purity.</p>
                            @error('is_active')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Submit Button -->
                        <div class="flex items-center justify-end space-x-2">
                            <a href="{{ route('metal-rates.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update Metal Rate
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const metalTypeSelect = document.getElementById('metal_type');
            const puritySelect = document.getElementById('purity');
            const ratePerGramInput = document.getElementById('rate_per_gram');
            const ratePer10GramInput = document.getElementById('rate_per_10_gram');

            // Purity options based on metal type
            const purityOptions = {
                'Gold': ['22K', '18K', '14K'],
                'Silver': ['925', '999'],
                'Platinum': ['950', '900']
            };

            // Current values for pre-selection
            const currentMetalType = '{{ $metalRate->metal_type }}';
            const currentPurity = '{{ $metalRate->purity }}';

            function updatePurityOptions() {
                const selectedMetal = metalTypeSelect.value;
                puritySelect.innerHTML = '<option value="">Select Purity</option>';
                
                if (selectedMetal && purityOptions[selectedMetal]) {
                    purityOptions[selectedMetal].forEach(function(purity) {
                        const option = document.createElement('option');
                        option.value = purity;
                        option.textContent = purity;
                        if (purity === currentPurity) {
                            option.selected = true;
                        }
                        puritySelect.appendChild(option);
                    });
                }
            }

            function updateRatePer10Gram() {
                const ratePerGram = parseFloat(ratePerGramInput.value) || 0;
                ratePer10GramInput.value = (ratePerGram * 10).toFixed(2);
            }

            // Initialize purity options on page load
            updatePurityOptions();

            // Event listeners
            metalTypeSelect.addEventListener('change', updatePurityOptions);
            ratePerGramInput.addEventListener('input', updateRatePer10Gram);

            // Initialize rate per 10 gram calculation
            updateRatePer10Gram();
        });
    </script>
</x-app-layout>
