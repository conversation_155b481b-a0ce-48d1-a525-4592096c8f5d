<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\OldGoldPurchase;
use App\Models\Customer;
use App\Models\MetalRate;
use Illuminate\Support\Facades\DB;

class OldGoldPurchaseController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:view_old_gold_purchases')->only(['index', 'show']);
        $this->middleware('permission:create_old_gold_purchase')->only(['create', 'store']);
        $this->middleware('permission:edit_old_gold_purchase')->only(['edit', 'update']);
        $this->middleware('permission:delete_old_gold_purchase')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = OldGoldPurchase::with(['customer', 'createdBy']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('purchase_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('mobile', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by metal type
        if ($request->filled('metal_type')) {
            $query->where('metal_type', $request->metal_type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('from_date')) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }
        if ($request->filled('to_date')) {
            $query->whereDate('created_at', '<=', $request->to_date);
        }

        $purchases = $query->latest()->paginate(15);

        return view('old-gold-purchases.index', compact('purchases'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $customers = Customer::active()->orderBy('name')->get();

        // Get current metal rates
        $metalRates = MetalRate::where('is_active', true)
            ->where('effective_date', '<=', today())
            ->orderBy('effective_date', 'desc')
            ->get()
            ->groupBy(['metal_type', 'purity'])
            ->map(function ($group) {
                return $group->first();
            });

        return view('old-gold-purchases.create', compact('customers', 'metalRates'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'metal_type' => 'required|string|in:Gold,Silver',
            'purity' => 'required|string',
            'gross_weight' => 'required|numeric|min:0',
            'stone_weight' => 'nullable|numeric|min:0',
            'melting_loss_percentage' => 'nullable|numeric|min:0|max:100',
            'rate_per_gram' => 'required|numeric|min:0',
            'voucher_amount' => 'nullable|numeric|min:0',
            'cash_paid' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        DB::transaction(function () use ($validated) {
            // Calculate weights and amounts
            $grossWeight = $validated['gross_weight'];
            $stoneWeight = $validated['stone_weight'] ?? 0;
            $netWeight = $grossWeight - $stoneWeight;

            $meltingLossPercentage = $validated['melting_loss_percentage'] ?? 0;
            $meltingLossWeight = ($netWeight * $meltingLossPercentage) / 100;
            $finalWeight = $netWeight - $meltingLossWeight;

            $ratePerGram = $validated['rate_per_gram'];
            $totalAmount = $finalWeight * $ratePerGram;

            $voucherAmount = $validated['voucher_amount'] ?? 0;
            $cashPaid = $validated['cash_paid'] ?? 0;

            // Determine status based on payment method
            $status = 'purchased';
            if ($voucherAmount > 0 && $cashPaid == 0) {
                $status = 'converted_to_voucher';
            }

            // Create old gold purchase
            OldGoldPurchase::create([
                'customer_id' => $validated['customer_id'],
                'metal_type' => $validated['metal_type'],
                'purity' => $validated['purity'],
                'gross_weight' => $grossWeight,
                'stone_weight' => $stoneWeight,
                'net_weight' => $netWeight,
                'melting_loss_percentage' => $meltingLossPercentage,
                'melting_loss_weight' => $meltingLossWeight,
                'final_weight' => $finalWeight,
                'rate_per_gram' => $ratePerGram,
                'total_amount' => $totalAmount,
                'voucher_amount' => $voucherAmount,
                'cash_paid' => $cashPaid,
                'status' => $status,
                'notes' => $validated['notes'],
                'created_by' => auth()->id(),
            ]);
        });

        return redirect()->route('old-gold-purchases.index')
            ->with('success', 'Old gold purchase recorded successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(OldGoldPurchase $oldGoldPurchase)
    {
        $oldGoldPurchase->load(['customer', 'usedInSale', 'createdBy']);

        return view('old-gold-purchases.show', compact('oldGoldPurchase'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(OldGoldPurchase $oldGoldPurchase)
    {
        $customers = Customer::active()->orderBy('name')->get();

        // Get current metal rates
        $metalRates = MetalRate::where('is_active', true)
            ->where('effective_date', '<=', today())
            ->orderBy('effective_date', 'desc')
            ->get()
            ->groupBy(['metal_type', 'purity'])
            ->map(function ($group) {
                return $group->first();
            });

        return view('old-gold-purchases.edit', compact('oldGoldPurchase', 'customers', 'metalRates'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, OldGoldPurchase $oldGoldPurchase)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'metal_type' => 'required|string|in:Gold,Silver',
            'purity' => 'required|string',
            'gross_weight' => 'required|numeric|min:0',
            'stone_weight' => 'nullable|numeric|min:0',
            'melting_loss_percentage' => 'nullable|numeric|min:0|max:100',
            'rate_per_gram' => 'required|numeric|min:0',
            'voucher_amount' => 'nullable|numeric|min:0',
            'cash_paid' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
            'status' => 'required|in:purchased,converted_to_voucher,used_in_exchange',
        ]);

        // Calculate weights and amounts
        $grossWeight = $validated['gross_weight'];
        $stoneWeight = $validated['stone_weight'] ?? 0;
        $netWeight = $grossWeight - $stoneWeight;

        $meltingLossPercentage = $validated['melting_loss_percentage'] ?? 0;
        $meltingLossWeight = ($netWeight * $meltingLossPercentage) / 100;
        $finalWeight = $netWeight - $meltingLossWeight;

        $ratePerGram = $validated['rate_per_gram'];
        $totalAmount = $finalWeight * $ratePerGram;

        $voucherAmount = $validated['voucher_amount'] ?? 0;
        $cashPaid = $validated['cash_paid'] ?? 0;

        $oldGoldPurchase->update([
            'customer_id' => $validated['customer_id'],
            'metal_type' => $validated['metal_type'],
            'purity' => $validated['purity'],
            'gross_weight' => $grossWeight,
            'stone_weight' => $stoneWeight,
            'net_weight' => $netWeight,
            'melting_loss_percentage' => $meltingLossPercentage,
            'melting_loss_weight' => $meltingLossWeight,
            'final_weight' => $finalWeight,
            'rate_per_gram' => $ratePerGram,
            'total_amount' => $totalAmount,
            'voucher_amount' => $voucherAmount,
            'cash_paid' => $cashPaid,
            'status' => $validated['status'],
            'notes' => $validated['notes'],
        ]);

        return redirect()->route('old-gold-purchases.index')
            ->with('success', 'Old gold purchase updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OldGoldPurchase $oldGoldPurchase)
    {
        // Check if it's used in any sale
        if ($oldGoldPurchase->status === 'used_in_exchange') {
            return redirect()->route('old-gold-purchases.index')
                ->with('error', 'Cannot delete old gold purchase that has been used in exchange.');
        }

        $oldGoldPurchase->delete();

        return redirect()->route('old-gold-purchases.index')
            ->with('success', 'Old gold purchase deleted successfully.');
    }

    /**
     * Get available old gold purchases for a customer (for exchange)
     */
    public function getAvailableForCustomer(Request $request, $customer)
    {
        // Validate customer exists
        $customerId = (int) $customer;
        if (!Customer::find($customerId)) {
            return response()->json(['error' => 'Customer not found'], 404);
        }

        $purchases = OldGoldPurchase::where('customer_id', $customerId)
            ->where('status', '!=', 'used_in_exchange')
            ->where('voucher_amount', '>', 0)
            ->get(['id', 'purchase_number', 'metal_type', 'purity', 'final_weight', 'voucher_amount']);

        return response()->json($purchases);
    }
}
