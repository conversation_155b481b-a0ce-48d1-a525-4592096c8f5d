<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Estimate;
use App\Models\EstimateItem;
use App\Models\Customer;
use App\Models\MetalRate;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Product;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;

class EstimateController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:view_estimates')->only(['index', 'show']);
        $this->middleware('permission:create_estimate')->only(['create', 'store']);
        $this->middleware('permission:edit_estimate')->only(['edit', 'update']);
        $this->middleware('permission:delete_estimate')->only(['destroy']);
        $this->middleware('permission:convert_estimate')->only(['convert']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Estimate::with(['customer', 'estimateItems', 'createdBy']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('estimate_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('mobile', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('from_date')) {
            $query->whereDate('estimate_date', '>=', $request->from_date);
        }
        if ($request->filled('to_date')) {
            $query->whereDate('estimate_date', '<=', $request->to_date);
        }

        // Filter by validity
        if ($request->filled('validity')) {
            if ($request->validity === 'expired') {
                $query->where('valid_till', '<', today());
            } elseif ($request->validity === 'valid') {
                $query->where('valid_till', '>=', today());
            }
        }

        $estimates = $query->latest('estimate_date')->paginate(15);

        return view('estimates.index', compact('estimates'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $customers = Customer::active()->orderBy('name')->get();

        // Get current metal rates
        $metalRates = MetalRate::where('is_active', true)
            ->where('effective_date', '<=', today())
            ->orderBy('effective_date', 'desc')
            ->get()
            ->groupBy(['metal_type', 'purity'])
            ->map(function ($group) {
                return $group->first();
            });

        return view('estimates.create', compact('customers', 'metalRates'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'estimate_date' => 'required|date',
            'valid_till' => 'required|date|after:estimate_date',
            'rate_locked' => 'boolean',
            'items' => 'required|array|min:1',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.metal_type' => 'required|string',
            'items.*.purity' => 'required|string',
            'items.*.gross_weight' => 'required|numeric|min:0',
            'items.*.net_weight' => 'required|numeric|min:0',
            'items.*.stone_weight' => 'nullable|numeric|min:0',
            'items.*.metal_rate' => 'required|numeric|min:0',
            'items.*.making_charges' => 'required|numeric|min:0',
            'items.*.stone_charges' => 'nullable|numeric|min:0',
            'items.*.wastage_percentage' => 'nullable|numeric|min:0|max:100',
            'items.*.wastage_amount' => 'nullable|numeric|min:0',
            'items.*.item_total' => 'required|numeric|min:0',
            'items.*.description' => 'nullable|string',
            'discount_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        DB::transaction(function () use ($validated) {
            // Calculate totals
            $subtotal = 0;
            foreach ($validated['items'] as $item) {
                $subtotal += $item['item_total'];
            }

            $totalTax = $subtotal * 0.03; // 3% GST
            $discountAmount = $validated['discount_amount'] ?? 0;
            $totalAmount = $subtotal + $totalTax - $discountAmount;

            // Create estimate
            $estimate = Estimate::create([
                'customer_id' => $validated['customer_id'],
                'estimate_date' => $validated['estimate_date'],
                'valid_till' => $validated['valid_till'],
                'subtotal' => $subtotal,
                'total_tax' => $totalTax,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'rate_locked' => $validated['rate_locked'] ?? false,
                'status' => 'pending',
                'notes' => $validated['notes'],
                'created_by' => auth()->id(),
            ]);

            // Create estimate items
            foreach ($validated['items'] as $item) {
                EstimateItem::create([
                    'estimate_id' => $estimate->id,
                    'item_name' => $item['item_name'],
                    'metal_type' => $item['metal_type'],
                    'purity' => $item['purity'],
                    'gross_weight' => $item['gross_weight'],
                    'net_weight' => $item['net_weight'],
                    'stone_weight' => $item['stone_weight'] ?? 0,
                    'metal_rate' => $item['metal_rate'],
                    'making_charges' => $item['making_charges'],
                    'stone_charges' => $item['stone_charges'] ?? 0,
                    'wastage_percentage' => $item['wastage_percentage'] ?? 0,
                    'wastage_amount' => $item['wastage_amount'] ?? 0,
                    'item_total' => $item['item_total'],
                    'description' => $item['description'],
                ]);
            }
        });

        return redirect()->route('estimates.index')
            ->with('success', 'Estimate created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Estimate $estimate)
    {
        $estimate->load(['customer', 'estimateItems', 'convertedToSale', 'createdBy']);

        return view('estimates.show', compact('estimate'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Estimate $estimate)
    {
        if ($estimate->status === 'converted') {
            return redirect()->route('estimates.show', $estimate)
                ->with('error', 'Cannot edit converted estimate.');
        }

        $customers = Customer::active()->orderBy('name')->get();

        // Get current metal rates
        $metalRates = MetalRate::where('is_active', true)
            ->where('effective_date', '<=', today())
            ->orderBy('effective_date', 'desc')
            ->get()
            ->groupBy(['metal_type', 'purity'])
            ->map(function ($group) {
                return $group->first();
            });

        $estimate->load(['estimateItems']);

        return view('estimates.edit', compact('estimate', 'customers', 'metalRates'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Estimate $estimate)
    {
        if ($estimate->status === 'converted') {
            return redirect()->route('estimates.show', $estimate)
                ->with('error', 'Cannot update converted estimate.');
        }

        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'estimate_date' => 'required|date',
            'valid_till' => 'required|date|after:estimate_date',
            'rate_locked' => 'boolean',
            'discount_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,approved,rejected,expired',
        ]);

        $estimate->update([
            'customer_id' => $validated['customer_id'],
            'estimate_date' => $validated['estimate_date'],
            'valid_till' => $validated['valid_till'],
            'rate_locked' => $validated['rate_locked'] ?? false,
            'discount_amount' => $validated['discount_amount'] ?? 0,
            'notes' => $validated['notes'],
            'status' => $validated['status'],
        ]);

        return redirect()->route('estimates.index')
            ->with('success', 'Estimate updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Estimate $estimate)
    {
        if ($estimate->status === 'converted') {
            return redirect()->route('estimates.index')
                ->with('error', 'Cannot delete converted estimate.');
        }

        DB::transaction(function () use ($estimate) {
            $estimate->estimateItems()->delete();
            $estimate->delete();
        });

        return redirect()->route('estimates.index')
            ->with('success', 'Estimate deleted successfully.');
    }

    /**
     * Convert estimate to sale
     */
    public function convert(Estimate $estimate)
    {
        if (!$estimate->can_convert) {
            return redirect()->route('estimates.show', $estimate)
                ->with('error', 'This estimate cannot be converted to sale.');
        }

        return view('estimates.convert', compact('estimate'));
    }

    /**
     * Process estimate conversion to sale
     */
    public function processConversion(Request $request, Estimate $estimate)
    {
        if (!$estimate->can_convert) {
            return redirect()->route('estimates.show', $estimate)
                ->with('error', 'This estimate cannot be converted to sale.');
        }

        $validated = $request->validate([
            'sale_date' => 'required|date',
            'cash_payment' => 'nullable|numeric|min:0',
            'card_payment' => 'nullable|numeric|min:0',
            'upi_payment' => 'nullable|numeric|min:0',
            'old_gold_adjustment' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        DB::transaction(function () use ($validated, $estimate) {
            // Create sale from estimate
            $totalPayment = ($validated['cash_payment'] ?? 0) +
                           ($validated['card_payment'] ?? 0) +
                           ($validated['upi_payment'] ?? 0) +
                           ($validated['old_gold_adjustment'] ?? 0);

            $balanceAmount = $estimate->total_amount - $totalPayment;
            $paymentStatus = $balanceAmount <= 0 ? 'paid' : ($totalPayment > 0 ? 'partial' : 'pending');

            $sale = Sale::create([
                'customer_id' => $estimate->customer_id,
                'sale_date' => $validated['sale_date'],
                'subtotal' => $estimate->subtotal,
                'cgst_amount' => $estimate->total_tax / 2,
                'sgst_amount' => $estimate->total_tax / 2,
                'total_tax' => $estimate->total_tax,
                'discount_amount' => $estimate->discount_amount,
                'total_amount' => $estimate->total_amount,
                'cash_payment' => $validated['cash_payment'] ?? 0,
                'card_payment' => $validated['card_payment'] ?? 0,
                'upi_payment' => $validated['upi_payment'] ?? 0,
                'old_gold_adjustment' => $validated['old_gold_adjustment'] ?? 0,
                'balance_amount' => $balanceAmount,
                'payment_status' => $paymentStatus,
                'notes' => $validated['notes'],
                'created_by' => auth()->id(),
            ]);

            // Create products from estimate items and add to sale
            foreach ($estimate->estimateItems as $estimateItem) {
                // Create product from estimate item
                $product = Product::create([
                    'name' => $estimateItem->item_name,
                    'category' => 'Custom Order',
                    'metal_type' => $estimateItem->metal_type,
                    'purity' => $estimateItem->purity,
                    'gross_weight' => $estimateItem->gross_weight,
                    'net_weight' => $estimateItem->net_weight,
                    'stone_weight' => $estimateItem->stone_weight,
                    'wastage_percentage' => $estimateItem->wastage_percentage,
                    'making_charges' => $estimateItem->making_charges,
                    'stone_charges' => $estimateItem->stone_charges,
                    'cost_price' => $estimateItem->item_total * 0.8, // Assume 20% margin
                    'selling_price' => $estimateItem->item_total,
                    'quantity' => 1,
                    'description' => $estimateItem->description,
                    'status' => 'sold',
                ]);

                // Create sale item
                SaleItem::create([
                    'sale_id' => $sale->id,
                    'product_id' => $product->id,
                    'quantity' => 1,
                    'metal_rate' => $estimateItem->metal_rate,
                    'making_charges' => $estimateItem->making_charges,
                    'stone_charges' => $estimateItem->stone_charges,
                    'wastage_amount' => $estimateItem->wastage_amount,
                    'item_total' => $estimateItem->item_total,
                    'cgst_rate' => 1.5,
                    'sgst_rate' => 1.5,
                ]);
            }

            // Update customer totals
            $customer = $estimate->customer;
            $customer->total_purchases += $estimate->total_amount;
            $customer->total_orders += 1;
            $customer->save();

            // Mark estimate as converted
            $estimate->update([
                'status' => 'converted',
                'converted_to_sale_id' => $sale->id,
            ]);
        });

        return redirect()->route('sales.show', $estimate->convertedToSale)
            ->with('success', 'Estimate converted to sale successfully.');
    }

    /**
     * Generate PDF estimate
     */
    public function pdf(Estimate $estimate)
    {
        $estimate->load(['customer', 'estimateItems', 'createdBy']);

        $pdf = Pdf::loadView('estimates.pdf', compact('estimate'));

        return $pdf->download('estimate-' . $estimate->estimate_number . '.pdf');
    }
}
