<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes for better performance
        Schema::table('sales', function (Blueprint $table) {
            $table->index(['customer_id', 'sale_date']);
            $table->index('payment_status');
            $table->index('created_by');
        });

        Schema::table('sale_items', function (Blueprint $table) {
            $table->index('product_id');
        });

        Schema::table('products', function (Blueprint $table) {
            $table->index(['metal_type', 'purity']);
            $table->index('status');
            $table->index('category');
        });

        Schema::table('customers', function (Blueprint $table) {
            $table->index('is_active');
            $table->index(['date_of_birth', 'anniversary_date']);
        });

        Schema::table('old_gold_purchases', function (Blueprint $table) {
            $table->index(['customer_id', 'status']);
            $table->index(['metal_type', 'purity']);
            $table->index('voucher_amount');
        });

        Schema::table('metal_rates', function (Blueprint $table) {
            $table->index(['metal_type', 'purity', 'is_active']);
            $table->index('effective_date');
        });

        Schema::table('estimates', function (Blueprint $table) {
            $table->index(['customer_id', 'status']);
            $table->index('estimate_date');
        });

        Schema::table('repairs', function (Blueprint $table) {
            $table->index(['customer_id', 'status']);
            $table->index('received_date');
        });

        Schema::table('saving_schemes', function (Blueprint $table) {
            $table->index(['customer_id', 'status']);
            $table->index('start_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sales', function (Blueprint $table) {
            $table->dropIndex(['customer_id', 'sale_date']);
            $table->dropIndex(['payment_status']);
            $table->dropIndex(['created_by']);
        });

        Schema::table('sale_items', function (Blueprint $table) {
            $table->dropIndex(['product_id']);
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['metal_type', 'purity']);
            $table->dropIndex(['status']);
            $table->dropIndex(['category']);
        });

        Schema::table('customers', function (Blueprint $table) {
            $table->dropIndex(['is_active']);
            $table->dropIndex(['date_of_birth', 'anniversary_date']);
        });

        Schema::table('old_gold_purchases', function (Blueprint $table) {
            $table->dropIndex(['customer_id', 'status']);
            $table->dropIndex(['metal_type', 'purity']);
            $table->dropIndex(['voucher_amount']);
        });

        Schema::table('metal_rates', function (Blueprint $table) {
            $table->dropIndex(['metal_type', 'purity', 'is_active']);
            $table->dropIndex(['effective_date']);
        });

        Schema::table('estimates', function (Blueprint $table) {
            $table->dropIndex(['customer_id', 'status']);
            $table->dropIndex(['estimate_date']);
        });

        Schema::table('repairs', function (Blueprint $table) {
            $table->dropIndex(['customer_id', 'status']);
            $table->dropIndex(['received_date']);
        });

        Schema::table('saving_schemes', function (Blueprint $table) {
            $table->dropIndex(['customer_id', 'status']);
            $table->dropIndex(['start_date']);
        });
    }
};
