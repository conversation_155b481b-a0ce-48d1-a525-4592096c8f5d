<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Sale extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_number',
        'customer_id',
        'sale_date',
        'subtotal',
        'cgst_amount',
        'sgst_amount',
        'igst_amount',
        'total_tax',
        'discount_amount',
        'total_amount',
        'cash_payment',
        'card_payment',
        'upi_payment',
        'old_gold_adjustment',
        'balance_amount',
        'payment_status',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'sale_date' => 'date',
        'subtotal' => 'decimal:2',
        'cgst_amount' => 'decimal:2',
        'sgst_amount' => 'decimal:2',
        'igst_amount' => 'decimal:2',
        'total_tax' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'cash_payment' => 'decimal:2',
        'card_payment' => 'decimal:2',
        'upi_payment' => 'decimal:2',
        'old_gold_adjustment' => 'decimal:2',
        'balance_amount' => 'decimal:2',
    ];

    // Relationships
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function saleItems()
    {
        return $this->hasMany(SaleItem::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function oldGoldPurchases()
    {
        return $this->hasMany(OldGoldPurchase::class, 'used_in_sale_id');
    }

    // Accessors
    public function getTotalPaymentAttribute()
    {
        return $this->cash_payment + $this->card_payment + $this->upi_payment + $this->old_gold_adjustment;
    }

    public function getIsFullyPaidAttribute()
    {
        return $this->balance_amount <= 0;
    }

    // Scopes
    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    public function scopePending($query)
    {
        return $query->where('payment_status', 'pending');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('sale_date', today());
    }

    // Boot method to generate invoice number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($sale) {
            if (empty($sale->invoice_number)) {
                $sale->invoice_number = 'INV-' . date('Y') . '-' . str_pad(static::whereYear('created_at', date('Y'))->count() + 1, 6, '0', STR_PAD_LEFT);
            }
        });
    }
}
